<?php

namespace App\Filament\Resources;

use App\Enums\SubscriptionLicenses;
use App\Enums\Subscriptions;
use App\Helpers\StatusHelper;
use App\Helpers\BillingsHelper;
use App\Models\PlanFeature;
use App\Models\PlatformCountry;
use App\Observers\SubscriptionObserver;
use App\Services\SubscriptionService;
use App\Services\MoneyFormatter;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;
use App\Filament\Resources\SubscriptionResource\Pages;
use App\Filament\Resources\SubscriptionResource\RelationManagers;
use App\Helpers\AppHelper;
use App\Models\Subscription;
use App\Models\SuspensionReason;
use App\Models\Country;
use App\Models\Currency;
use App\Models\User;
use App\Models\Plan;
use TomatoPHP\FilamentSubscriptions\Facades\FilamentSubscriptions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\DynamicFields;
use App\Traits\HasShieldAccess;
use Filament\Forms\Components\Section;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\HtmlString;

class SubscriptionResource extends Resource
{
    use DynamicFields, HasShieldAccess;

    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';
    protected static ?string $navigationLabel = 'Subscriptions';
    protected static ?string $navigationGroup = 'Payments';
    protected static ?int $navigationSort = 2;

    /**
     * Required form fields
     * Set to ['*'] to make all columns required
     */
    protected static array $requiredFields = ['name', 'email'];

    /**
     * Calculate the subscription amount based on form values
     */
    protected static function calculateAmount(Forms\Get $get, $record = null): string
    {
        $planId = $get('plan_id');
        $billingCountryId = $get('billing_country_id');
        $currencyId = $get('currency_id');
        $numberOfYears = $get('number_of_years') ?: 1;

        // Get addon features from form
        $addonFeatures = $get('addon_features') ?: [];

        // Get main plan feature from form
        $mainPlanFeature = $get('plan_features');

        if ($mainPlanFeature) {
            $addonFeatures[] = $mainPlanFeature;
        }

        if (!$planId || !$billingCountryId) {
            return 'Please select plan and billing country';
        }


        try {
            $priceData = BillingsHelper::calculateEstimatedPrice(
                planId: $planId,
                billingCountryId: $billingCountryId,
                newCurrencyId: $currencyId,
                addons: $addonFeatures,
                platformCountryId: $get('country_id'),
                numberOfYears: $numberOfYears,
                subscriptionId: $record?->id
            );

            if (empty($priceData)) {
                return 'Unable to calculate amount';
            }

            $grandTotal = $priceData['grand_total'];
            $moneyFormatter = app(MoneyFormatter::class);

            return $moneyFormatter->price(
                $grandTotal->getAmount(),
                $grandTotal->getCurrency()->getCode()
            )->format();

        } catch (\Exception $e) {
            return 'Error calculating amount';
        }
    }

    /**
     * Excluded form fields
     * By default these are excluded: ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'deleted_by', 'two_factor_secret', 'two_factor_recovery_codes', 'two_factor_confirmed_at', 'otp', 'otp_expires_at']
     */
    protected static array $excludedFields = [
        'plan_id',
        'name',
        'subscriber_type',
        'subscriber_id',
        'slug',
        'trial_ends_at',
        'starts_at',
        'ends_at',
        'cancels_at',
        'canceled_at',
        'auto_renew',
        'country_id',
        'currency_id',
        'subscription_status',
        'payment_status',
        'last_payment_date',
        'assigned_quota',
        'used_quota',
        'remaining_quota',
        'billing_country_id',
        'number_of_years',
    ];

    /**
     * Wether the field should be a select in both Form & Table
     * Don't specify options in order to set the defaults: (Active, Inactive, Deleted)
     */
    protected static array $dropdownFields = [
        'timezone' => [
            'timezones' => true,
            'searchable' => true,
        ],
        'currency_id' => [
            'relationship' => [
                'model' => Currency::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'id',
            ],
            'searchable' => true,
            'default' => 1,
            'required' => true,
        ],
        'suspension_reason_id' => [
            'relationship' => [
                'model' => SuspensionReason::class,
                'titleAttribute' => 'name',
                'valueAttribute' => 'suspension_reasons_id',
            ],
            'searchable' => true,
        ],
    ];

    /**
     * Table filter fields
     * Set to ['*'] to make all columns filterable
     */
    protected static array $filterableFields = ['starts_at', 'ends_at'];

    /**
     * Excluded Table columns
     * By default these are excluded: ['password', 'remember_token']
     */
    protected static array $excludedTableFields = [];

    /**
     * Sortable Table columns
     * Set to ['*'] to make all columns sortable
     */
    protected static array $sortableTableFields = ['*'];

    /**
     * colors fields
     */
    protected static array $colorFields = ['color'];

    /**
     * Set it to false in order to disable table search
     */
    protected static bool $tableSearch = true;

    /**
     * map keys => labels
     */
    protected static array $attributesLabels = [
        'currency_id' => 'Currency',
        'country_id' => 'Country',
        'suspension_reason_id' => 'Suspension Reason',
    ];

    public static function getNavigationGroup(): ?string
    {
        return trans('filament-subscriptions::messages.group');
    }

    public static function getNavigationLabel(): string
    {
        return trans('filament-subscriptions::messages.subscriptions.title');
    }

    public static function getPluralLabel(): ?string
    {
        return trans('filament-subscriptions::messages.subscriptions.title');
    }

    public static function getLabel(): ?string
    {
        return trans('filament-subscriptions::messages.subscriptions.title');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //                Forms\Components\Hidden::make('name'),
                Forms\Components\Section::make('Subscriber Details')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                // model of the user that will subscribe
                                Forms\Components\Select::make('subscriber_type')
                                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.subscriber.columns.subscriber_type'))
                                    ->required()
                                    ->options(count(FilamentSubscriptions::getOptions()) ? FilamentSubscriptions::getOptions()->pluck('name', 'model')->toArray() : [User::class => 'Users'])
                                    ->afterStateUpdated(fn(Forms\Get $get, Forms\Set $set) => $set('subscriber_id', null))
                                    ->preload()
                                    ->live()
                                    ->searchable(),
                                Forms\Components\Select::make('subscriber_id')
                                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.subscriber.columns.subscriber'))
                                    ->required()
                                    ->options(function (Forms\Get $get) {
                                        $subscriberType = $get('subscriber_type');

                                        if ($subscriberType) {
                                            $model = (string) $subscriberType;
                                            $key = (new $model)->getKeyName();

                                            return (new $model)->whereNotNull('name')
                                                ->whereNot('status_id', StatusHelper::getStatusByAttributeName('name', 'pending'))
                                                ->pluck('name', $key)
                                                ->toArray();
                                        }

                                        return [];
                                    })
                                    ->live()
                                    ->searchable(),
                            ]),

                        Forms\Components\Select::make('country_id')
                            ->label('Deployment Country')
                            ->options(function () {
                                return PlatformCountry::all()->pluck('name', 'platform_country_id')->toArray();
                            })
                            ->searchable()
                            ->required(function (Forms\Get $get) {
                                $planId = $get('plan_id');
                                if (!$planId) return true;

                                $plan = Plan::find($planId);
                                // Make the field not required if it's a trial plan
                                return !($plan && $plan->isFreeTrial());
                            })
                            ->live()
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                // Recalculate amount when deployment country changes
                                $set('calculated_amount', self::calculateAmount($get));
                            }),
                    ]),

                Forms\Components\Section::make('Payment Details')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('currency_id')
                                    ->label('Currency')
                                    ->options(Currency::all()->pluck('name', 'id')->toArray())
                                    ->default(Currency::where('iso', 'USD')->first()->id)
                                    ->searchable()
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                        // Recalculate amount when currency changes
                                        $set('calculated_amount', self::calculateAmount($get));
                                    }),

                                Forms\Components\Select::make('payment_status')
                                    ->options(Subscriptions::getPaymentStatusOptions())
                                    ->default(Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING)
                                    ->required()
                                    ->disabled()
                                    ->dehydrated(true),
                            ]),

                        Forms\Components\TextInput::make('calculated_amount')
                            ->label('Amount')
                            ->readOnly()
                            ->dehydrated(false)
                            ->default('Please select plan and billing country')
                            ->formatStateUsing(function ($state, Forms\Get $get, $record) {
                                // Calculate initial amount when form loads
                                return self::calculateAmount($get, $record);
                            })
                            ->helperText('This amount is calculated automatically based on selected plan, features, add-ons, and billing details'),

                        Forms\Components\Select::make('billing_country_id')
                            ->label('Billing Country')
                            ->options(function (Forms\Get $get) {
                                $subscriberType = $get('subscriber_type');
                                $subscriberId = $get('subscriber_id');

                                if ($subscriberType && $subscriberId && $subscriberType === \App\Models\Company::class) {
                                    $company = \App\Models\Company::find($subscriberId);
                                    if ($company) {
                                        // Return only the countries associated with this company
                                        return $company->countries->pluck('name', 'id')->toArray();
                                    }
                                }

                                // Fallback to all countries if no company selected or company has no countries
                                return Country::all()->pluck('name', 'id')->toArray();
                            })
                            ->searchable()
                            ->required(function (Forms\Get $get) {
                                $planId = $get('plan_id');
                                if (!$planId) return true;

                                $plan = Plan::find($planId);
                                // Make the field not required if it's a trial plan
                                return !($plan && $plan->isFreeTrial());
                            })
                            ->live()
                            ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                // Recalculate amount when billing country changes
                                $set('calculated_amount', self::calculateAmount($get));
                            }),
                    ]),

                Forms\Components\Section::make('Payment Rejection Details')
                    ->description('You can check history for more details')
                    ->schema([
                        Forms\Components\TextInput::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->helperText('Last rejection reason from payment history')
                            ->formatStateUsing(function ($state, $record) {
                                if (!$record) return '-';

                                $receipt = $record->subscriptionReceipts()
                                    ->where('receipt_status', 'rejected')
                                    ->latest()
                                    ->first();

                                return $receipt ? $receipt->rejection_reason : '-';
                            })
                            ->readOnly(),

                        Forms\Components\TextInput::make('rejection_reason_description')
                            ->label('Rejection Comment')
                            ->helperText('Last rejection comment from payment history')
                            ->formatStateUsing(function ($state, $record) {
                                if (!$record) return '-';

                                $history = $record->subscriptionHistories()
                                    ->where('history_event', 'payment_rejected')
                                    ->latest()
                                    ->first();

                                return $history ? $history->rejection_reason_description : '-';
                            })
                            ->readOnly()
                    ])
                    ->visible(function ($record) {
                        if (!$record) return false;

                        // Check if there's a rejected receipt
                        $hasRejectedReceipt = $record->subscriptionReceipts()
                            ->where('receipt_status', 'rejected')
                            ->exists();

                        // Check if there's a payment rejection history
                        $hasRejectionHistory = $record->subscriptionHistories()
                            ->where('history_event', 'payment_rejected')
                            ->exists();

                        return $hasRejectedReceipt || $hasRejectionHistory;
                    }),

                Forms\Components\Section::make('Subscription Details')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('plan_id')
                                    ->searchable()
                                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.plan.columns.plan'))
                                    ->options(Plan::query()->where('is_active', 1)->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set, $state) {
                                        // Only update name if plan_id is not null
                                        if ($state) {
                                            // Reset features when changing plan
                                            $set('subscription_type', null);
                                            $set('plan_features', null);
                                            $set('addon_features', []);
                                        }
                                        // Recalculate amount when plan changes
                                        $set('calculated_amount', self::calculateAmount($get));
                                    })
                                    ->required()
                                    ->live(),

                                // Select subscription type (SaaS or On-Prem) from plan features
                                Forms\Components\Select::make('subscription_type')
                                    ->label('Subscription Type')
                                    ->options(function (Forms\Get $get) {
                                        $planId = $get('plan_id');
                                        if (!$planId) return [];

                                        $plan = Plan::find($planId);
                                        if (!$plan) return [];

                                        // Get top-level features (subscription types)
                                        $features = $plan->planFeatures()
                                            ->where('parent_plan_feature_id', null)
                                            ->with('feature')
                                            ->get();

                                        return $features->mapWithKeys(function ($planFeature) {
                                            return [
                                                $planFeature->plan_feature_id => $planFeature->feature->name
                                            ];
                                        })->toArray();
                                    })
                                    ->required()
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set, $state) {

                                        // Reset plan features and addons when changing subscription type
                                        $set('plan_features', null);
                                        $set('addon_features', []);

                                        // Get the selected plan and feature to check if it's On-Prem + Trial
                                        $planId = $get('plan_id');
                                        if ($planId && $state) {
                                            $plan = Plan::find($planId);
                                            $planFeature = PlanFeature::find($state);

                                            // Check if it's a trial plan and On-Prem subscription type
                                            if ($plan && $plan->isFreeTrial() && $planFeature) {
                                                $feature = $planFeature->feature;
                                                if ($feature && str_contains($feature->slug, Subscription::SUBSCRIPTION_TYPE_ON_PREM)) {
                                                    // Auto-set subscription status to active and payment status to paid
                                                    $set('subscription_status', Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value);
                                                    $set('payment_status', Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID->value);
                                                }
                                            }
                                        }
                                    })
                                    ->searchable(),

                                // Handle plan features
                                Forms\Components\Select::make('plan_features')
                                    ->label('Plan Features')
                                    ->options(function (Forms\Get $get) {
                                        $planId = $get('plan_id');
                                        $subscriptionTypeId = $get('subscription_type');

                                        if (!$planId || !$subscriptionTypeId) return [];

                                        // Get plan features that have the subscription type as parent and are not addons
                                        $planFeatures = PlanFeature::where('plan_id', $planId)
                                            ->where('parent_plan_feature_id', $subscriptionTypeId)
                                            ->where('is_addon', false)
                                            ->with('feature')
                                            ->get();

                                        return $planFeatures->mapWithKeys(function ($planFeature) {
                                            $minValue = $planFeature->valid_from_minimum ? " (Min: {$planFeature->valid_from_minimum})" : '';
                                            $maxValue = $planFeature->valid_to_maximum ? " (Max: {$planFeature->valid_to_maximum})" : '';
                                            $price = $planFeature->price ? " - Price: {$planFeature->price} USD" : '';
                                            return [
                                                $planFeature->plan_feature_id => "{$planFeature->feature->name}{$minValue}{$maxValue}{$price}"
                                            ];
                                        })->toArray();
                                    })
                                    ->searchable()
                                    ->live()
                                    ->required(function (Forms\Get $get) {
                                        $planId = $get('plan_id');
                                        if (!$planId) return false;

                                        $plan = Plan::find($planId);
                                        // Make the field not required if it's a trial plan
                                        return !($plan && $plan->isFreeTrial());
                                    })
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set, $state) {
                                        $planFeature = PlanFeature::find($state);
                                        $assignedQuota = 0;

                                        if ($planFeature && $planFeature->valid_to_maximum) {
                                            $assignedQuota = $planFeature->valid_to_maximum;
                                        }

                                        // Update assigned_quota
                                        $set('assigned_quota', $assignedQuota);

                                        // Recalculate remaining_quota based on new assigned value and current used value
                                        $usedQuota = (int) $get('used_quota');
                                        $set('remaining_quota', $assignedQuota - $usedQuota);

                                        // Reset addon features when plan features change
                                        $set('addon_features', []);

                                        // Recalculate amount when plan features change
                                        $set('calculated_amount', self::calculateAmount($get));
                                    })
                                    ->helperText(function (Forms\Get $get) {
                                        $planId = $get('plan_id');
                                        $subscriptionTypeId = $get('subscription_type');

                                        if (!$planId) {
                                            return 'Please select a plan first to see available features.';
                                        }

                                        if (!$subscriptionTypeId) {
                                            return 'Please select a subscription type to filter features.';
                                        }

                                        $plan = Plan::find($planId);
                                        if ($plan && $plan->isFreeTrial()) {
                                            return 'Feature selection is optional for trial plans.';
                                        }

                                        return 'Select the main plan feature for this subscription.';
                                    }),

                                Forms\Components\Select::make('addon_features')
                                    ->label('Add-ons')
                                    ->options(function (Forms\Get $get) {
                                        $planId = $get('plan_id');
                                        $subscriptionTypeId = $get('subscription_type');
                                        $planFeatureId = $get('plan_features');

                                        if (!$planId || !$planFeatureId) return [];

                                        // Get the selected plan feature to extract its min/max values
                                        $selectedPlanFeature = PlanFeature::find($planFeatureId);
                                        if (!$selectedPlanFeature) return [];

                                        $validFromMin = $selectedPlanFeature->valid_from_minimum;
                                        $validToMax = $selectedPlanFeature->valid_to_maximum;

                                        // Get all addon plan features that have the selected plan feature as parent
                                        $allAddons = PlanFeature::where([
                                            'plan_id' => $planId,
                                            'parent_plan_feature_id' => $planFeatureId,
                                            'is_addon' => true
                                        ])->get();

                                        // Apply the same filtering logic as getPlanFeatureAddons function
                                        $filteredAddons = $allAddons->filter(function ($addon) use ($validFromMin, $validToMax) {
                                            // Include both regular addons and extra addons (treat them the same)
                                            if ($addon->is_addon) {
                                                if (!is_null($validFromMin) && !is_null($validToMax)) {
                                                    // If the selected plan feature has min/max values, filter addons accordingly
                                                    return $addon->valid_from_minimum <= $validFromMin && $addon->valid_to_maximum >= $validToMax;
                                                } else {
                                                    // If the selected plan feature has no min/max, only show addons with no min/max
                                                    return $addon->valid_from_minimum == null && $addon->valid_to_maximum == null;
                                                }
                                            }
                                            return false;
                                        });

                                        return $filteredAddons->mapWithKeys(function ($planFeature) {
                                            $price = $planFeature->price ? " - Price: {$planFeature->price} USD" : '';
                                            return [
                                                $planFeature->plan_feature_id => "{$planFeature->feature->name}{$price}"
                                            ];
                                        })->toArray();
                                    })
                                    ->searchable()
                                    ->multiple()
                                    ->live()
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                        // Recalculate amount when addon features change
                                        $set('calculated_amount', self::calculateAmount($get));
                                    })
                                    ->helperText('Optional add-ons available for your selected Line of Code (Plan Features)'),


                                Forms\Components\TextInput::make('assigned_quota')
                                    ->numeric()
                                    ->readOnly()
                                    ->dehydrated(true)
                                    ->helperText('This value is automatically set based on selected plan features'),

                                Forms\Components\TextInput::make('used_quota')
                                    ->numeric()
                                    ->default(0)
                                    ->live(onBlur: true)
                                    ->readOnly(fn($livewire) => $livewire instanceof \App\Filament\Resources\SubscriptionResource\Pages\CreateSubscription)
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set, $state) {
                                        // When used_quota changes, recalculate remaining_quota
                                        $assigned = (int) $get('assigned_quota');
                                        $used = (int) $state;
                                        $set('remaining_quota', $assigned - $used);
                                    }),

                                Forms\Components\TextInput::make('remaining_quota')
                                    ->numeric()
                                    ->helperText('This value is automatically set based on selected plan features')
                                    ->readOnly(true),

                                Forms\Components\Select::make('subscription_status')
                                    ->options(Subscriptions::getSubscriptionStatusOptions())
                                    ->default('pending_payment')
                                    ->required(),

                                Forms\Components\TextInput::make('name')
                                    ->readOnly()
                                    ->default(Subscription::DEFAULT_NAME)
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('number_of_years')
                                    //                                    ->default(Subscription::DEFAULT_NAME)
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set) {
                                        // Recalculate amount when number of years changes
                                        $set('calculated_amount', self::calculateAmount($get));
                                    })
                                    ->formatStateUsing(function ($state, $record, Forms\Get $get) {
                                        // If it's a trial plan, show 0, but keep the real value in the database
                                        $planId = $get('plan_id');
                                        if ($planId) {
                                            $plan = \App\Models\Plan::find($planId);
                                            if ($plan && $plan->isFreeTrial()) {
                                                return 0;
                                            }
                                        }
                                        return $state;
                                    }),

                                ...self::getFormFields(self::$model),

                            ]),
                    ]),

                Forms\Components\Section::make('Subscription License Details')
                    ->schema([
                        Forms\Components\Repeater::make('subscription_licenses')
                            ->schema([
                                Forms\Components\Hidden::make('subscription_license_id'),
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\Select::make('license_type')
                                            ->options(Subscriptions::getLicenseTypeOptions())
                                            ->default(Subscriptions::LICENSE_TYPE_EVALUATION->value)
                                            ->required(),
                                        Forms\Components\Select::make('environment')
                                            ->options(Subscriptions::getEnvironmentOptions())
                                            ->default(Subscriptions::ENVIRONMENT_OPTION_PRODUCTION->value)
                                            ->required(),
                                        Forms\Components\TextInput::make('license_key')
                                            ->disabled(function ($record) { // Accept the $record parameter here
                                                if (!$record) {
                                                    return false;
                                                }

                                                $isActive = optional($record->getActiveEnvironment())->environment_status === SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value;

                                                return $record->isSaas() && !$isActive;
                                            }),
                                        Forms\Components\TextInput::make('server_id')
                                            ->required()
                                            ->rule(function (callable $get, $record) {
                                                return function (string $attribute, $value, \Closure $fail) use ($get, $record) {
                                                    // Get the current subscription license ID if we're editing
                                                    $currentLicenseId = $get('subscription_license_id');

                                                    // Build the unique validation query
                                                    $query = \App\Models\SubscriptionLicense::where('server_id', $value);

                                                    // If we're editing an existing license, exclude it from the check
                                                    if ($currentLicenseId) {
                                                        $query->where('subscription_license_id', '!=', $currentLicenseId);
                                                    }

                                                    // Check if any other record has this server_id
                                                    if ($query->exists()) {
                                                        $fail('The server id has already been taken.');
                                                    }
                                                };
                                            })
                                            ->rules([
                                                'string',
                                                new \App\Rules\SonarServerId
                                            ]),
                                    ]),
                            ])
                            ->addActionLabel('Add License')
                            ->defaultItems(1)
                            ->reorderable(false),
                    ]),

                Forms\Components\Toggle::make('auto_renew')
                    ->columnSpanFull()
                    ->label('Auto Renew'),
                Forms\Components\Toggle::make('use_custom_dates')
                    ->columnSpanFull()
                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.plan.columns.use_custom_dates'))
                    ->live(),
                Forms\Components\DatePicker::make('trial_ends_at')
                    ->visible(fn(Forms\Get $get) => $get('use_custom_dates'))
                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.custom_dates.columns.trial_ends_at'))
                    ->required(fn(Forms\Get $get) => $get('use_custom_dates')),
                Forms\Components\DatePicker::make('starts_at')
                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.custom_dates.columns.starts_at')),
                Forms\Components\DatePicker::make('ends_at')
                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.custom_dates.columns.ends_at')),
                Forms\Components\DatePicker::make('canceled_at')
                    ->visible(fn(Forms\Get $get) => $get('use_custom_dates'))
                    ->label(trans('filament-subscriptions::messages.subscriptions.sections.custom_dates.columns.canceled_at'))
                    ->required(fn(Forms\Get $get) => $get('use_custom_dates')),
                Forms\Components\DatePicker::make('last_payment_date')
                    ->visible(fn(Forms\Get $get) => $get('use_custom_dates'))
                    ->label('Last Payment Date')
                    ->nullable(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('public_subscription_id')
                    ->label('Public ID')
                    ->sortable()
                    ->searchable(),


                Tables\Columns\TextColumn::make('environment_display_status')
                    ->label('Environment')
                    ->getStateUsing(function (Subscription $record): ?string {
                        return $record->getActiveEnvironment()?->environment_status;
                    })
                    ->sortable()
                    ->badge()
                    ->formatStateUsing(function (?string $state): string {
                        if ($state === null) {
                            return '-';
                        }
                        return SubscriptionLicenses::getLabel($state) ?? ucfirst($state);
                    })
                    ->color(fn(?string $state): string => match ($state) {
                        SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value ?? 'active' => 'success',
                        SubscriptionLicenses::ENVIRONMENT_STATUS_PENDING->value ?? 'pending' => 'warning',
                        SubscriptionLicenses::ENVIRONMENT_STATUS_FAILED->value ?? 'failed' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('subscriber.name')
                    ->label(trans('filament-subscriptions::messages.subscriptions.columns.subscriber'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('plan.name')
                    ->label(trans('filament-subscriptions::messages.subscriptions.columns.plan'))
                    ->sortable()
                    ->searchable(),


                Tables\Columns\TextColumn::make('subscription_type')
                    ->label('Subscription Type')
                    ->state(function ($record) {
                        $subscriptionFeatures = $record->subscriptionFeatures()
                            ->with('feature')
                            ->get();

                        $subscriptionTypeFeature = $subscriptionFeatures
                            ->first(function ($feature) {
                                return str_contains($feature->feature->slug, Subscription::SUBSCRIPTION_TYPE_SAAS) ||
                                    str_contains($feature->feature->slug, Subscription::SUBSCRIPTION_TYPE_ON_PREM);
                            });

                        return $subscriptionTypeFeature ? $subscriptionTypeFeature->feature->name : '-';
                    }),
                Tables\Columns\TextColumn::make('subscription_status')
                    ->label('Subscription Status')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => Subscriptions::getLabel($state) ?? $state)
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'pending_payment' => 'warning',
                        'pending_license_key', 'pending_activation', 'pending_receipt' => 'info',
                        'expired', 'suspended', 'terminated' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('payment_status')
                    ->label('Payment Status')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => Subscriptions::getLabel($state) ?? $state)
                    ->color(fn(string $state): string => match ($state) {
                        'paid', 'free' => 'success',
                        'failed', 'unpaid' => 'danger',
                        'in_review' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Amount')
                    ->getStateUsing(function (Subscription $record): string {
                        try {
                            $totalPrice = $record->totalPrice(true);
                            if (empty($totalPrice) || !isset($totalPrice['amount_formatted'])) {
                                return '-';
                            }
                            return $totalPrice['amount_formatted'];
                        } catch (\Exception $e) {
                            return 'Error';
                        }
                    })
                    ->sortable(false)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('suspensionReason.name')
                    ->label('Suspension Reason')
                    ->sortable()
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('subscriptionLatestPayment.payment_type')
                    ->label('Payment Type')
                    ->sortable()
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subscriptionLatestPayment.created_at')
                    ->label('Payment Date')
                    ->sortable()
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\IconColumn::make('has_pending_receipts')
                    ->label('Pending Receipts')
                    ->boolean()
                    ->getStateUsing(function (Subscription $record): bool {
                        return $record && $record->subscriptionReceipts()->where('receipt_status', Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING)->exists();
                    })
                    ->trueIcon('heroicon-o-exclamation-circle')
                    ->falseIcon('')
                    ->trueColor('warning'),

                Tables\Columns\TextColumn::make('receipt_status')
                    ->label('Receipt Status')
                    ->getStateUsing(function (Subscription $record) {
                        if (!$record) return '-';

                        $receipt = $record->subscriptionReceipts()->latest()->first();
                        return $receipt ? $receipt->receipt_status : '-';
                    })
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'pending' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('rejection_reason_description')
                    ->label('Rejection Comment')
                    ->getStateUsing(function (Subscription $record) {
                        if (!$record) return '-';

                        $history = $record->subscriptionHistories()
                            ->where('history_event', 'payment_rejected')
                            ->latest()
                            ->first();

                        return $history ? $history->rejection_reason_description : '-';
                    })
                    ->wrap()
                    ->limit(50)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('subscriptionFeatures')
                    ->label('Plan Features')
                    ->formatStateUsing(function ($record) {
                        $features = $record->subscriptionFeatures()->with('feature')->get();
                        if ($features->isEmpty()) return 'None';

                        return $features->take(3)->map(function ($feature) {
                            return $feature->feature?->name;
                        })->implode(', ') . ($features->count() > 3 ? '...' : '');
                    }),

                Tables\Columns\TextColumn::make('created_by')
                    ->label('Created By')
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    })
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_by')
                    ->label('Updated By')
                    ->formatStateUsing(function ($state) {
                        if (!$state) return '-';
                        $user = \App\Models\User::find($state);
                        return $user ? $user->name : $state;
                    })
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('trial_ends_at')->dateTime()
                    ->label(trans('filament-subscriptions::messages.subscriptions.columns.trial_ends_at'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('starts_at')->dateTime()
                    ->label(trans('filament-subscriptions::messages.subscriptions.columns.starts_at'))
                    ->sortable()
                    ->toggleable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('ends_at')->dateTime()
                    ->label(trans('filament-subscriptions::messages.subscriptions.columns.ends_at'))
                    ->sortable()
                    ->toggleable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('canceled_at')->dateTime()
                    ->label(trans('filament-subscriptions::messages.subscriptions.columns.canceled_at'))
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Tables\Filters\Filter::make('has_pending_receipts')
                    ->label('Has Pending Receipts')
                    ->query(function (Builder $query) {
                        return $query->whereHas('subscriptionReceipts', function (Builder $query) {
                            $query->where('receipt_status', Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PENDING);
                        });
                    })
                    ->toggle(),
                Tables\Filters\Filter::make('missing_license')
                    ->label('Pending License Key')
                    ->query(function (Builder $query) {
                        return $query->where(function ($subQuery) {
                            // Group all conditions within a single where clause to maintain AND relationship with other filters
                            $subQuery->where('payment_status', Subscriptions::SUBSCRIPTION_PAYMENT_STATUS_PAID->value)
                                ->whereHas('subscriptionLicenses', function (Builder $licenseQuery) {
                                    $licenseQuery->whereNotNull('server_id')
                                        ->where(function ($q) {
                                            $q->whereNull('license_key')
                                                ->orWhere('license_key', '');
                                        });
                                })
                                ->orWhere('subscription_status', Subscriptions::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY);
                        });
                    })
                    ->toggle(),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->label('Payment Status')
                    ->options(Subscriptions::getPaymentStatusOptions())
                    ->attribute('payment_status'),
                Tables\Filters\SelectFilter::make('subscription_status')
                    ->label('Subscription Status')
                    ->options(Subscriptions::getSubscriptionStatusOptions())
                    ->attribute('subscription_status'),
                Tables\Filters\SelectFilter::make('plan_id')
                    ->label('Plan')
                    ->relationship('plan', 'name')
                    ->attribute('plan_id'),
                Tables\Filters\SelectFilter::make('subscription_type')
                    ->label('Subscription Type')
                    ->options(Subscription::getSubscriptionTypes())
                    ->query(function (Builder $query, array $data) {
                        if (!$data['value']) {
                            return $query;
                        }

                        return $query->whereHas('subscriptionFeatures', function (Builder $subQuery) use ($data) {
                            $subQuery->whereHas('feature', function (Builder $featureQuery) use ($data) {
                                $featureQuery->where('slug', 'like', $data['value'] . '%');
                            });
                        });
                    }),
                ...self::getTableFilters(self::$model),
            ])
            ->actions([

                Tables\Actions\Action::make('viewEnvironmentDetailsModal')
                    ->label('Environment Details')
                    ->icon('heroicon-o-globe-alt')
                    ->modalHeading('Environment Details')
                    ->modalContent(function (?Subscription $record) {

                        $activeProcessDetails = $record->getActiveEnvironment();

                        if ($activeProcessDetails) {
                            return view('filament.modals.environment-details', [
                                'serverId' => $activeProcessDetails->server_id ?? 'N/A',
                                'sonarUrl' => $activeProcessDetails->sonar_url ?? 'N/A',
                                'sonarUsername' => $activeProcessDetails->sonar_username ?? 'N/A',
                                'sonarPassword' => AppHelper::decryptString($activeProcessDetails->sonar_password) ?? 'N/A',
                            ]);
                        }

                        return null;
                    })
                    ->visible(function (?Subscription $record): bool {
                        $processDetails = $record->getActiveEnvironment();

                        if (!empty($processDetails)) {
                            if (AppHelper::matchStrings($processDetails->environment_status, SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value)) {
                                return true;
                            }
                        }

                        return false;
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close')
                    ->authorize(true), // Ensure action can be called

                self::getTableLicenseUpdateAction(),
                Tables\Actions\ViewAction::make()
                    ->tooltip(__('filament-actions::view.single.label'))
                    ->iconButton(),
                Tables\Actions\EditAction::make()
                    ->tooltip(__('filament-actions::edit.single.label'))
                    ->iconButton(),
                Tables\Actions\Action::make('cancel')
                    ->visible(fn($record) => $record->active() && auth()->user()->can('update_subscription'))
                    ->iconButton()
                    ->label(trans('filament-subscriptions::messages.subscriptions.actions.cancel'))
                    ->tooltip(trans('filament-subscriptions::messages.subscriptions.actions.cancel'))
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Subscription $record) {
                        $record->cancel(true);

                        Notification::make()
                            ->title(trans('filament-subscriptions::messages.notifications.cancel.title'))
                            ->body(trans('filament-subscriptions::messages.notifications.cancel.message'))
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation(),
                Tables\Actions\Action::make('renew')
                    ->visible(fn($record) => $record->ended())
                    ->iconButton()
                    ->label(trans('filament-subscriptions::messages.subscriptions.actions.renew'))
                    ->tooltip(trans('filament-subscriptions::messages.subscriptions.actions.renew'))
                    ->icon('heroicon-o-arrow-path-rounded-square')
                    ->color('info')
                    ->action(function (Subscription $record) {
                        $record->canceled_at =  Carbon::parse($record->cancels_at)->addDays(1);
                        $record->cancels_at = Carbon::parse($record->cancels_at)->addDays(1);
                        $record->ends_at =  Carbon::parse($record->cancels_at)->addDays(1);
                        $record->save();
                        $record->renew();

                        Notification::make()
                            ->title(trans('filament-subscriptions::messages.notifications.renew.title'))
                            ->body(trans('filament-subscriptions::messages.notifications.renew.message'))
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation(),
                Tables\Actions\DeleteAction::make()
                    ->tooltip(__('filament-actions::delete.single.label'))
                    ->iconButton(),
                Tables\Actions\ForceDeleteAction::make()
                    ->tooltip(__('filament-actions::force-delete.single.label'))
                    ->iconButton(),
                Tables\Actions\RestoreAction::make()
                    ->tooltip(__('filament-actions::restore.single.label'))
                    ->iconButton(),
            ])
            ->defaultPaginationPageOption(25)
            ->recordUrl(
                fn(Subscription $record): string => static::getUrl('view', ['record' => $record])
            )
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make()
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ReceiptsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'view' => Pages\ViewSubscription::route('/{record}'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    // Define the action logic in a reusable method
    public static function handleLicenseUpdate(Subscription $record, array $data): void
    {
        $subscriptionService = app(SubscriptionService::class);

        // Update only the license information
        $subscriptionService->updateLicenses($record, $data['subscription_licenses']);

        // Update the subscription status to active if it was pending a license key
        if ($record->subscription_status === Subscriptions::SUBSCRIPTION_STATUS_PENDING_LICENSE_KEY->value) {
            SubscriptionObserver::$eventType = Subscriptions::HISTORY_EVENT_TYPE_ACTIVATION->value;

            $record->update(['subscription_status' => Subscriptions::SUBSCRIPTION_STATUS_ACTIVE->value]);
        }

        Notification::make()
            ->title('License information updated successfully')
            ->success()
            ->send();
    }

    // Define the form schema and action logic in a reusable method
    public static function getLicenseUpdateForm(Subscription $record): array
    {
        // Get existing license data
        $licenses = $record->subscriptionLicenses()->get()->toArray();

        return [
            Section::make('License Information')
                ->schema([
                    Forms\Components\Repeater::make('subscription_licenses')
                        ->schema([
                            Forms\Components\Hidden::make('subscription_license_id'),
                            Forms\Components\Grid::make(2)
                                ->schema([
                                    Forms\Components\Select::make('license_type')
                                        ->options(Subscriptions::getLicenseTypeOptions())
                                        ->default(Subscriptions::LICENSE_TYPE_EVALUATION->value)
                                        ->required(),
                                    Forms\Components\Select::make('environment')
                                        ->options(Subscriptions::getEnvironmentOptions())
                                        ->default(Subscriptions::ENVIRONMENT_OPTION_PRODUCTION->value)
                                        ->required(),
                                    Forms\Components\TextInput::make('license_key')
                                        ->disabled(function ($record) { // Accept the $record parameter here
                                            if (!$record) {
                                                return false;
                                            }

                                            $isActive = optional($record->getActiveEnvironment())->environment_status === SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value;

                                            return $record->isSaas() && !$isActive;
                                        }),
                                    Forms\Components\TextInput::make('server_id')
                                        ->required()
                                        ->rule(function (callable $get, $record) {
                                            return function (string $attribute, $value, \Closure $fail) use ($get, $record) {
                                                // Get the current subscription license ID if we're editing
                                                $currentLicenseId = $get('subscription_license_id');

                                                // Build the unique validation query
                                                $query = \App\Models\SubscriptionLicense::where('server_id', $value);

                                                // If we're editing an existing license, exclude it from the check
                                                if ($currentLicenseId) {
                                                    $query->where('subscription_license_id', '!=', $currentLicenseId);
                                                }

                                                // Check if any other record has this server_id
                                                if ($query->exists()) {
                                                    $fail('The server id has already been taken.');
                                                }
                                            };
                                        })
                                        ->rules([
                                            'string',
                                            new \App\Rules\SonarServerId
                                        ]),
                                ]),
                        ])
                        ->addActionLabel('Add License')
                        ->defaultItems(count($licenses) > 0 ? 0 : 1)
                        ->reorderable(false)
                        ->default($licenses),
                ])
        ];
    }

    // Create a table action
    public static function getTableLicenseUpdateAction(): Tables\Actions\Action
    {
        return Tables\Actions\Action::make('updateLicense')
            ->label('Update License')
            ->icon('heroicon-o-key')
            ->color('primary')
            ->tooltip('Update license key information')
            ->visible(function ($record) {
                if (!$record) {
                    return false;
                }

                $isActive = optional($record->getActiveEnvironment())->environment_status === SubscriptionLicenses::ENVIRONMENT_STATUS_ACTIVE->value;

                // Hide if SaaS and environment is active
                if ($record->isSaas() && !$isActive) {
                    return false;
                }

                return auth()->user()->can('update_subscription::license');
            })
            ->form(function (Subscription $record) {
                return self::getLicenseUpdateForm($record);
            })
            ->action(function (Subscription $record, array $data) {
                self::handleLicenseUpdate($record, $data);
            });
    }
}
